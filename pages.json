{
	"pages": [{
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTextStyle": "white",
				"navigationBarTitleText": "业务员助手",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "登录",
				"navigationStyle": "custom",
				"usingComponents": {
					"van-field": "/wxcomponents/vant/field/index"
				}
			}
		},
		{
			"path": "pages/workbenches/workbenches",
			"style": {
				"navigationBarTitleText": "工作台",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/mine/mine",
			"style": {
				"navigationBarTitleText": "我的",
				"navigationStyle": "custom",
				"usingComponents": {
					"van-cell": "/wxcomponents/vant/cell/index"
				}
			}
		}
	],
	"subPackages": [{
			"root": "pages/subMine", // 分包A的根目录
			"pages": [{
				"path": "setting/setting",
				"style": {
					"navigationBarTitleText": "设置"
				}
			}]
		},
		{
			"root": "pages/subVisit", // 分包B的根目录
			"pages": [{
					"path": "createVisit/createVisit", //打卡
					"style": {
						"navigationBarTitleText": "终端拜访",
						"usingComponents": {
							"van-checkbox": "/wxcomponents/vant/checkbox/index",
							"van-radio": "/wxcomponents/vant/radio/index",
  							"van-radio-group": "/wxcomponents/vant/radio-group/index"
						}
					}
				},
				{
					"path": "selectSub/selectSub",
					"style": {
						"navigationBarTitleText": "选择下属"
					}
				},
				{
					"path": "addAgent/addAgent",
					"style": {
						"navigationBarTitleText": "新增代理商代表",
						"usingComponents": {
							"van-cell-group": "/wxcomponents/vant/cell-group/index",
							"van-field": "/wxcomponents/vant/field/index",
							"van-button": "/wxcomponents/vant/button/index",
							"van-cell": "/wxcomponents/vant/cell/index"
						}
					}
				},
				{
					"path": "selectAgent/selectAgent",
					"style": {
						"navigationBarTitleText": "选择代理商"
					}
				},
				{
					"path": "selectAgent/agentChild",
					"style": {
						"navigationBarTitleText": "选择代表"
					}
				},
				{
					"path": "selectClientShop/selectClientShop",
					"style": {
						"navigationBarTitleText": "选择客户" //选择客户 医院
					}
				},
				{
					"path": "selectClientDoctor/selectClientDoctor",
					"style": {
						"navigationBarTitleText": "选择客户", //选择科室 医生
						"usingComponents": {
							"van-tree-select": "/wxcomponents/vant/tree-select/index",
							"van-popup": "/wxcomponents/vant/popup/index",
							"van-button": "/wxcomponents/vant/button/index",
							"van-collapse": "/wxcomponents/vant/collapse/index",
                            "van-collapse-item": "/wxcomponents/vant/collapse-item/index"
						}
					}
				},
				{
					"path": "addDoctor/addDoctor",
					"style": {
						"navigationBarTitleText": "新增专家", //选择科室 医生
						"usingComponents": {
							"van-cell-group": "/wxcomponents/vant/cell-group/index",
							"van-field": "/wxcomponents/vant/field/index",
							"van-button": "/wxcomponents/vant/button/index",
							"van-cell": "/wxcomponents/vant/cell/index"
						}
					}
				},
				{
					"path": "visitLog/visitLog", //拜访日志
					"style": {
						"navigationBarTitleText": "拜访日志",
						"usingComponents": {
							"van-cell": "/wxcomponents/vant/cell/index"
						}
					}
				},
				{
					"path": "signRecord/signRecord", //打卡记录
					"style": {
						"navigationBarTitleText": "打卡记录",
						"usingComponents": {}
					}
				},
				{
					"path": "comment/comment", //协访评价
					"style": {
						"navigationBarTitleText": "协访评价",
						"usingComponents": {}
					}
				}
			]
		}, {
			"root": "pages/subMeeting", // 会议部分的根目录
			"pages": [{
					"path": "meetingList/meetingList",
					"style": {
						"navigationBarTitleText": "会议列表"
					}
				},
				{
					"path": "meetingBooking/meetingBooking",
					"style": {
						"navigationBarTitleText": "预定会议",
						"usingComponents": {
							"van-cell": "/wxcomponents/vant/cell/index",
							"van-steps": "/wxcomponents/vant/steps/index",
							"uv-datetime-picker": "../../uni_modules/uv-datetime-picker/components/uv-datetime-picker/uv-datetime-picker"
						}
					}
				},
				{
					"path": "selectSpeaker/selectSpeaker",
					"style": {
						"navigationBarTitleText": "选择讲者",
						"usingComponents": {
							"van-checkbox": "/wxcomponents/vant/checkbox/index"
						}
					}
				},
				{
					"path": "createSpeaker/createSpeaker",
					"style": {
						"navigationBarTitleText": "新建讲者",
						"usingComponents": {
							"van-cell": "/wxcomponents/vant/cell/index"
						}
					}
				},
				{
					"path": "selectTerminal/selectTerminal",
					"style": {
						"navigationBarTitleText": "选择终端",
						"usingComponents": {
							"van-checkbox": "/wxcomponents/vant/checkbox/index"
						}
					}
				},
				{
					"path": "selectDepartment/selectDepartment",
					"style": {
						"navigationBarTitleText": "选择科室",
						"usingComponents": {
							"van-checkbox": "/wxcomponents/vant/checkbox/index",
							"van-tree-select": "/wxcomponents/vant/tree-select/index"
						}
					}
				},
				{
					"path": "selectResponsiblePerson/selectResponsiblePerson",
					"style": {
						"navigationBarTitleText": "选择责任人",
						"usingComponents": {
							"van-checkbox": "/wxcomponents/vant/checkbox/index",
							"van-tree-select": "/wxcomponents/vant/tree-select/index"
						}
					}
				},
				{
					"path": "selectMeetingPeople/selectMeetingPeople",
					"style": {
						"navigationBarTitleText": "选择参会人",
						"usingComponents": {
							"van-checkbox": "/wxcomponents/vant/checkbox/index",
							"van-tree-select": "/wxcomponents/vant/tree-select/index"
						}
					}
				},
				{
					"path": "meetingDetails/meetingDetails",
					"style": {
						"navigationBarTitleText": "会议详情",
						"usingComponents": {
							"van-cell": "/wxcomponents/vant/cell/index"
							// "van-steps": "/wxcomponents/vant/steps/index"
						}
					}
				},
				{
					"path": "teamMeetingDetails/teamMeetingDetails",
					"style": {
						"navigationBarTitleText": "会议详情",
						"usingComponents": {
							"van-cell": "/wxcomponents/vant/cell/index"
							// "van-steps": "/wxcomponents/vant/steps/index"
						}
					}
				},
				{
					"path": "meetingProduct/meetingProduct",
					"style": {
						"navigationBarTitleText": "会议产品",
						"usingComponents": {
							"van-cell": "/wxcomponents/vant/cell/index",
							"van-checkbox": "/wxcomponents/vant/checkbox/index"
						}
					}
				}
			]
		}, {
			"root": "pages/subTerminal",
			"pages": [{
					"path": "terminalList/terminalList",
					"style": {
						"navigationBarTitleText": "终端开发" //终端开发列表页面
					}
				},
				{
					"path": "terminalDetails/terminalDetails",
					"style": {
						"navigationBarTitleText": "终端详情" //终端开发详情页
					}
				},
				{
					"path": "terminalEdit/terminalEdit",
					"style": {
						"navigationBarTitleText": "终端详情", //终端开发编辑页面
						"usingComponents": {
							"van-cell": "/wxcomponents/vant/cell/index"
						}
					}
				},
				{
					"path": "terminalEditSumUp/terminalEditSumUp",
					"style": {
						"navigationBarTitleText": "终端开发总结"
					}
				}
			]
		}, {
			"root": "pages/subReport",
			"pages": [{
					"path": "reportList/reportList",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "createReport/createReport",
					"style": {
						"navigationBarTitleText": "",
						"usingComponents": {
							"van-field": "/wxcomponents/vant/field/index",
							"van-calendar": "/wxcomponents/vant/calendar/index"
						}
					}
				},
				{
					"path": "reportDetails/reportDetails",
					"style": {
						"navigationBarTitleText": ""
					}
				}
			]
		},{
			"root": "subPackage/agentDA", //代理商档案
			"pages": [{
					"path": "index",
					"style": {
						"navigationBarTitleText": "代理商档案",
						"usingComponents": {
							"van-field": "/wxcomponents/vant/field/index",
							"van-icon": "/wxcomponents/vant/icon/index",
							"van-swipe-cell": "/wxcomponents/vant/swipe-cell/index",
							"van-loading": "/wxcomponents/vant/loading/index",
							"van-popup": "/wxcomponents/vant/popup/index",
							"van-picker": "/wxcomponents/vant/picker/index"
						}
					}
				},
				{
					"path": "add",
					"style": {
						"navigationBarTitleText": "新增储备代理商",
						"usingComponents": {
							"van-field": "/wxcomponents/vant/field/index",
							"van-icon": "/wxcomponents/vant/icon/index",
							"van-radio": "/wxcomponents/vant/radio/index",
							"van-radio-group": "/wxcomponents/vant/radio-group/index",
							"van-popup": "/wxcomponents/vant/popup/index",
							"van-picker": "/wxcomponents/vant/picker/index",
							"van-button": "/wxcomponents/vant/button/index"
						}
					}
				},
				{
					"path": "detail",
					"style": {
						"navigationBarTitleText": "储备代理商详情",
						"usingComponents": {
							"van-icon": "/wxcomponents/vant/icon/index",
							"van-tag": "/wxcomponents/vant/tag/index"
						}
					}
				},
				{
					"path": "detailZ",
					"style": {
						"navigationBarTitleText": "正式代理商详情"
					}
				}
			]
		}
	],
	"preloadRule": {
		"pages/home/<USER>": {
			"network": "all", // 网络环境
			"packages": ["pages/subMine", "pages/subVisit", "pages/subMeeting", "pages/subTerminal",
				"pages/subReport"] // 预加载的分包
		}
	},

	"globalStyle": {
		"backgroundColor": "#F8F8F8",
		"navigationBarBackgroundColor": "#FFFFFF",
		"navigationBarTitleText": "精细化招商代理",
		"navigationBarTextStyle": "black"
	},
	"easycom": {
		"autoscan": true,
		"custom": {
			"^p-(.*)": "@/components/publicComponents/p-$1/p-$1.vue"
		}
	},
	"uniIdRouter": {},
	"tabBar": {
		"custom": true,
		"list": [{
				"pagePath": "pages/home/<USER>",
				"iconPath": "/static/image/tabbar/home.png",
				"selectedIconPath": "/static/image/tabbar/home_checked.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/workbenches/workbenches",
				"iconPath": "/static/image/tabbar/work.png",
				"selectedIconPath": "/static/image/tabbar/home_checked.png",
				"text": "工作台"
			},
			{
				"pagePath": "pages/mine/mine",
				"iconPath": "/static/image/tabbar/mine.png",
				"selectedIconPath": "/static/image/tabbar/mine_checked.png",
				"text": "我的"
			}
		]
	}
}
